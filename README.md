# ZapGap API

A Deno-based API server that integrates with Langflow for chat functionality.

## Features

- **POST /chat endpoint** - Accepts user messages and forwards them to Langflow API
- **Environment-based configuration** - Secure handling of API tokens and endpoints
- **Streaming support** - Handles both regular JSON and streaming responses from Langflow
- **TypeScript support** - Full type safety with Langflow response interfaces
- **Error handling** - Comprehensive error handling for network failures and API errors

## Setup

1. **Clone and navigate to the project:**
   ```bash
   cd zapgap-api
   ```

2. **Configure environment variables:**
   ```bash
   cp .env.example .env
   ```

   Edit `.env` with your actual Langflow credentials:
   ```env
   LANGFLOW_API_TOKEN=your_actual_token_here
   LANGFLOW_BASE_URL=https://api.langflow.astra.datastax.com
   LANGFLOW_FLOW_ID=your_flow_id_here
   LANGFLOW_ENDPOINT_ID=your_endpoint_id_here
   ```

3. **Start the server:**
   ```bash
   deno task start
   ```

The server will start on `http://localhost:8000`

## API Endpoints

### POST /chat

Send a message to the Langflow API and receive a response.

**Request:**
```json
{
  "msg": "Your message here"
}
```

**Response (Success):**
```json
{
  "session_id": "user_1",
  "outputs": [
    {
      "inputs": {
        "input_value": "Your message here"
      },
      "outputs": [
        {
          "results": {
            "message": {
              "text": "AI response here",
              "sender": "Machine",
              "sender_name": "AI",
              // ... additional fields
            }
          },
          // ... additional output data
        }
      ]
    }
  ]
}
```

**Response (Error):**
```json
{
  "error": "Error description",
  "message": "Detailed error message"
}
```

**Example Usage:**
```bash
curl -X POST http://localhost:8000/chat \
  -H "Content-Type: application/json" \
  -d '{"msg": "Hello, how are you?"}'
```

### GET /

Health check endpoint that returns "Hello Hono!"

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `LANGFLOW_API_TOKEN` | **Required** - Your Langflow API token | - |
| `LANGFLOW_BASE_URL` | Langflow API base URL | `https://api.langflow.astra.datastax.com` |
| `LANGFLOW_FLOW_ID` | Your Langflow flow ID | `76cf76a8-2a7c-4df4-9f32-cc0799b81d0f` |
| `LANGFLOW_ENDPOINT_ID` | Your Langflow endpoint ID | `3c295853-e107-448b-a370-17e3aa47945e` |

## Development

The project uses:
- **Deno** - Runtime environment
- **Hono** - Web framework
- **TypeScript** - Type safety

### Project Structure

```
├── main.ts              # Main server file with API endpoints
├── langflow.ts          # TypeScript interfaces for Langflow API
├── deno.json           # Deno configuration and tasks
├── .env                # Environment variables (create from .env.example)
├── .env.example        # Environment variables template
└── README.md           # This file
```

### Error Handling

The API includes comprehensive error handling for:
- Missing or invalid request fields (400 Bad Request)
- Langflow API errors (502 Bad Gateway)
- Network failures (500 Internal Server Error)
- Environment configuration issues (Server startup failure)

### Streaming Support

The endpoint automatically detects and forwards streaming responses from the Langflow API when the content type indicates streaming (`text/event-stream` or `application/stream`).

## License

MIT
