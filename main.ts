import { Hono } from 'hono'
import { LangFlowResponse } from './langflow.ts'

// Load environment variables from .env file
try {
  const envText = await Deno.readTextFile('.env')
  const envLines = envText.split('\n')
  for (const line of envLines) {
    const trimmed = line.trim()
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=')
      if (key && valueParts.length > 0) {
        Deno.env.set(key, valueParts.join('='))
      }
    }
  }
} catch (error) {
  console.warn('Could not load .env file:', error instanceof Error ? error.message : 'Unknown error')
}

const app = new Hono()

// Environment variables validation
const LANGFLOW_API_TOKEN = Deno.env.get('LANGFLOW_API_TOKEN')
const LANGFLOW_BASE_URL = Deno.env.get('LANGFLOW_BASE_URL') || 'https://api.langflow.astra.datastax.com'
const LANGFLOW_FLOW_ID = Deno.env.get('LANGFLOW_FLOW_ID') || '76cf76a8-2a7c-4df4-9f32-cc0799b81d0f'
const LANGFLOW_ENDPOINT_ID = Deno.env.get('LANGFLOW_ENDPOINT_ID') || '3c295853-e107-448b-a370-17e3aa47945e'

if (!LANGFLOW_API_TOKEN) {
  console.error('LANGFLOW_API_TOKEN environment variable is required')
  Deno.exit(1)
}

// Request/Response interfaces
interface ChatRequest {
  msg: string
}

interface LangflowApiRequest {
  input_value: string
  output_type: string
  input_type: string
  session_id: string
}

app.get('/', (c) => {
  return c.text('Hello Hono!')
})

// POST /chat endpoint
app.post('/chat', async (c) => {
  try {
    // Parse request body
    const body = await c.req.json() as ChatRequest

    // Validate request
    if (!body.msg || typeof body.msg !== 'string') {
      return c.json({ error: 'Missing or invalid "msg" field in request body' }, 400)
    }

    // Prepare Langflow API request
    const langflowRequest: LangflowApiRequest = {
      input_value: body.msg,
      output_type: 'chat',
      input_type: 'chat',
      session_id: 'user_1'
    }

    // Build Langflow API URL
    const langflowUrl = `${LANGFLOW_BASE_URL}/lf/${LANGFLOW_ENDPOINT_ID}/api/v1/run/${LANGFLOW_FLOW_ID}`

    // Make request to Langflow API
    const response = await fetch(langflowUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${LANGFLOW_API_TOKEN}`
      },
      body: JSON.stringify(langflowRequest)
    })

    // Handle non-200 responses
    if (!response.ok) {
      console.error(`Langflow API error: ${response.status} ${response.statusText}`)
      return c.json({
        error: 'External API error',
        status: response.status
      }, 502) // Bad Gateway
    }

    // Check if response is streaming
    const contentType = response.headers.get('content-type')
    if (contentType?.includes('text/event-stream') || contentType?.includes('application/stream')) {
      // Handle streaming response
      return new Response(response.body, {
        headers: {
          'Content-Type': contentType,
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive'
        }
      })
    } else {
      // Handle regular JSON response
      const langflowResponse: LangFlowResponse = await response.json()
      return c.json(langflowResponse)
    }

  } catch (error) {
    console.error('Chat endpoint error:', error)
    return c.json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

Deno.serve(app.fetch)
